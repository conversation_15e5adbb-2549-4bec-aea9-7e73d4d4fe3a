import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/init',
    name: 'Init',
    component: () => import('@/view/init/index.vue')
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/view/login/index.vue')
  },
  {
    path: '/scanUpload',
    name: 'ScanUpload',
    meta: {
      title: '扫码上传',
      client: true
    },
    component: () => import('@/view/example/upload/scanUpload.vue')
  },
  {
    path: '/chat-test',
    name: 'ChatTest',
    meta: {
      title: '聊天测试',
      client: true
    },
    component: () => import('@/view/chatManager/test.vue')
  },
  {
    path: '/group-member-test',
    name: 'GroupMemberTest',
    meta: {
      title: '群成员测试',
      client: true
    },
    component: () => import('@/view/chatManager/GroupMemberTest.vue')
  },
  {
    path: '/group-member-demo',
    name: 'GroupMemberDemo',
    meta: {
      title: '群成员演示',
      client: true
    },
    component: () => import('@/view/chatManager/GroupMemberDemo.vue')
  },
  {
    path: '/:catchAll(.*)',
    meta: {
      closeTab: true
    },
    component: () => import('@/view/error/index.vue')
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
