<template>
  <div class="group-member-test">
    <div class="test-header">
      <h2>群成员功能测试</h2>
      <el-button @click="loadTestData" type="primary">加载测试数据</el-button>
      <el-button @click="refreshMembers" type="success">刷新成员</el-button>
    </div>

    <div class="test-content">
      <div class="group-info">
        <h3>当前群组信息</h3>
        <el-card v-if="currentGroup">
          <div class="group-details">
            <p><strong>群组ID:</strong> {{ currentGroup.ID }}</p>
            <p><strong>群组名称:</strong> {{ currentGroup.GroupName }}</p>
            <p><strong>群主ID:</strong> {{ currentGroup.GroupHave }}</p>
            <p><strong>管理员:</strong> {{ currentGroup.Admins?.join(', ') || '无' }}</p>
            <p><strong>成员ID列表:</strong> {{ currentGroup.FromID?.join(', ') || '无' }}</p>
            <p><strong>成员数量:</strong> {{ currentGroup.FromID?.length || 0 }}</p>
          </div>
        </el-card>
        <el-empty v-else description="暂无群组数据" />
      </div>

      <div class="member-panel">
        <h3>群成员面板</h3>
        <div class="panel-container">
          <GroupUserPanel 
            v-if="currentGroup" 
            :group="currentGroup"
            @member-click="handleMemberClick"
            @start-chat="handleStartChat"
          />
          <el-empty v-else description="请先加载测试数据" />
        </div>
      </div>
    </div>

    <!-- 成员详情弹窗 -->
    <el-dialog v-model="memberDialogVisible" title="成员详情" width="400px">
      <div v-if="selectedMember">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="用户ID">{{ selectedMember.id }}</el-descriptions-item>
          <el-descriptions-item label="昵称">{{ selectedMember.nickname }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ selectedMember.phone }}</el-descriptions-item>
          <el-descriptions-item label="在线状态">
            <el-tag :type="selectedMember.online ? 'success' : 'info'">
              {{ selectedMember.online ? '在线' : '离线' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="角色">
            <el-tag v-if="selectedMember.isOwner" type="danger">群主</el-tag>
            <el-tag v-else-if="selectedMember.isAdmin" type="warning">管理员</el-tag>
            <el-tag v-else type="info">普通成员</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="最后登录">{{ selectedMember.lastLoginTime }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import GroupUserPanel from './components/GroupUserPanel.vue'
import { getGroupList } from '@/api/im/group'

defineOptions({
  name: 'GroupMemberTest'
})

// 响应式数据
const currentGroup = ref(null)
const memberDialogVisible = ref(false)
const selectedMember = ref(null)

// 加载测试数据
const loadTestData = async () => {
  try {
    ElMessage.info('正在获取群组数据...')
    
    // 获取群组列表
    const response = await getGroupList({
      page: 1,
      pageSize: 10
    })

    if (response.code === 0 && response.data && response.data.list && response.data.list.length > 0) {
      // 使用第一个群组作为测试数据
      currentGroup.value = response.data.list[0]
      ElMessage.success('测试数据加载成功')
      console.log('当前群组数据:', currentGroup.value)
    } else {
      ElMessage.warning('没有找到群组数据')
      console.log('API响应:', response)
    }
  } catch (error) {
    console.error('加载测试数据失败:', error)
    ElMessage.error('加载测试数据失败')
  }
}

// 刷新成员
const refreshMembers = () => {
  if (currentGroup.value) {
    // 触发GroupUserPanel组件重新获取成员数据
    ElMessage.info('正在刷新群成员数据...')
  } else {
    ElMessage.warning('请先加载群组数据')
  }
}

// 处理成员点击
const handleMemberClick = (member) => {
  console.log('点击成员:', member)
  selectedMember.value = member
  memberDialogVisible.value = true
}

// 处理开始私聊
const handleStartChat = (member) => {
  console.log('开始私聊:', member)
  ElMessage.success(`准备与 ${member.nickname} 开始私聊`)
}

// 组件挂载时自动加载数据
onMounted(() => {
  loadTestData()
})
</script>

<style lang="scss" scoped>
.group-member-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .test-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;

    h2 {
      margin: 0;
      color: #1f2937;
    }
  }

  .test-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 24px;
    height: 600px;

    .group-info {
      .group-details {
        p {
          margin: 8px 0;
          font-size: 14px;
          
          strong {
            color: #374151;
          }
        }
      }
    }

    .member-panel {
      .panel-container {
        height: 500px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        overflow: hidden;
      }
    }
  }
}

@media (max-width: 768px) {
  .group-member-test {
    .test-content {
      grid-template-columns: 1fr;
      height: auto;

      .member-panel {
        .panel-container {
          height: 400px;
        }
      }
    }
  }
}
</style>
