import { login, getUserInfo } from '@/api/user'
import { jsonInBlacklist } from '@/api/jwt'
import router from '@/router/index'
import { ElLoading, ElMessage } from 'element-plus'
import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { useRouterStore } from './router'
import { useCookies } from '@vueuse/integrations/useCookies'
import { useStorage } from '@vueuse/core'

import { useAppStore } from '@/pinia'

export const useUserStore = defineStore('user', () => {
  const appStore = useAppStore()
  const loadingInstance = ref(null)

  const userInfo = ref({
    uuid: '',
    nickName: '',
    headerImg: '',
    authority: {}
  })
  const token = useStorage('token', '')
  const xToken = useCookies('x-token')
  const currentToken = computed(() => token.value || xToken.value || '')

  const setUserInfo = (val) => {
    userInfo.value = val
    if (val.originSetting) {
      Object.keys(appStore.config).forEach((key) => {
        if (val.originSetting[key] !== undefined) {
          appStore.config[key] = val.originSetting[key]
        }
      })
    }
    console.log(appStore.config)
  }

  const setToken = (val) => {
    token.value = val
    xToken.value = val
  }

  const NeedInit = async () => {
    await ClearStorage()
    await router.push({ name: 'Init', replace: true })
  }

  const ResetUserInfo = (value = {}) => {
    userInfo.value = {
      ...userInfo.value,
      ...value
    }
  }
  /* 获取用户信息*/
  const GetUserInfo = async () => {
    const res = await getUserInfo()
    if (res.code === 0) {
      setUserInfo(res.data.userInfo)
    }
    return res
  }
  /* 登录*/
  const LoginIn = async (loginInfo) => {
    try {
      loadingInstance.value = ElLoading.service({
        fullscreen: true,
        text: '登录中，请稍候...'
      })

      const res = await login(loginInfo)

      if (res.code !== 0) {
        ElMessage.error(res.message || '登录失败')
        return false
      }
      // 登陆成功，设置用户信息和权限相关信息
      setUserInfo(res.data.user)
      setToken(res.data.token)

      // 初始化路由信息
      const routerStore = useRouterStore()
      await routerStore.SetAsyncRouter()
      const asyncRouters = routerStore.asyncRouters

      // 注册到路由表里
      asyncRouters.forEach((asyncRouter) => {
        router.addRoute(asyncRouter)
      })

      if (!router.hasRoute(userInfo.value.authority.defaultRouter)) {
        ElMessage.error('请联系管理员进行授权')
      } else {
        await router.replace({ name: userInfo.value.authority.defaultRouter })
      }

      const isWindows = /windows/i.test(navigator.userAgent)
      window.localStorage.setItem('osType', isWindows ? 'WIN' : 'MAC')

      // 初始化WebSocket持久连接
      try {
        // 动态导入WebSocket store以避免循环依赖
        const { useWebSocketStore } = await import('@/pinia/modules/websocket')
        const webSocketStore = useWebSocketStore()

        // 启用持久连接模式
        webSocketStore.enablePersistentConnection()

        // 初始化连接（持久连接模式）
        const connected = await webSocketStore.initConnection(res.data.token, true)
        if (connected) {
          console.log('WebSocket持久连接初始化成功')
        } else {
          console.log('WebSocket初始连接失败，但持久连接机制已启用')
        }
      } catch (error) {
        console.error('WebSocket连接初始化失败:', error)
        // WebSocket连接失败不影响登录流程，只记录错误
        // 持久连接机制仍会在后台尝试重连
      }

      // 全部操作均结束，关闭loading并返回
      return true
    } catch (error) {
      console.error('LoginIn error:', error)
      return false
    } finally {
      loadingInstance.value?.close()
    }
  }

  /* 登出*/
  const LoginOut = async () => {
    const res = await jsonInBlacklist()

    // 登出失败
    if (res.code !== 0) {
      return
    }

    // 关闭WebSocket持久连接
    try {
      // 动态导入WebSocket store以避免循环依赖
      const { useWebSocketStore } = await import('@/pinia/modules/websocket')
      const webSocketStore = useWebSocketStore()

      // 禁用持久连接并关闭连接
      webSocketStore.disablePersistentConnection()
      webSocketStore.closeConnection()
      console.log('WebSocket持久连接已关闭')
    } catch (error) {
      console.error('关闭WebSocket连接时出错:', error)
    }

    await ClearStorage()

    // 把路由定向到登录页，无需等待直接reload
    router.push({ name: 'Login', replace: true })
    window.location.reload()
  }
  /* 清理数据 */
  const ClearStorage = async () => {
    token.value = ''
    xToken.value = ''
    sessionStorage.clear()
    localStorage.removeItem('originSetting')
  }

  // 监听 token 变化，自动初始化 WebSocket
  watch(currentToken, async (newToken, oldToken) => {
    // 只在页面刷新后 token 从空变为有值时触发
    if (newToken && !oldToken) {
      console.log('检测到页面刷新后 token 加载完成，初始化 WebSocket')

      // 延迟一点时间确保其他 store 也初始化完成
      setTimeout(async () => {
        try {
          const { useWebSocketStore } = await import('@/pinia/modules/websocket')
          const webSocketStore = useWebSocketStore()

          // 如果 WebSocket 未连接，则初始化连接
          if (!webSocketStore.isConnected) {
            console.log('页面刷新后自动初始化 WebSocket 连接')
            webSocketStore.enablePersistentConnection()

            try {
              await webSocketStore.initConnection(newToken, true)
              console.log('页面刷新后 WebSocket 自动连接成功')
            } catch (error) {
              console.log('页面刷新后 WebSocket 自动连接失败，持久连接机制将继续尝试:', error.message)
            }
          }
        } catch (error) {
          console.error('页面刷新后 WebSocket 初始化失败:', error)
        }
      }, 1000) // 延迟1秒
    }
  }, { immediate: true })

  return {
    userInfo,
    token: currentToken,
    NeedInit,
    ResetUserInfo,
    GetUserInfo,
    LoginIn,
    LoginOut,
    setToken,
    loadingInstance,
    ClearStorage
  }
})