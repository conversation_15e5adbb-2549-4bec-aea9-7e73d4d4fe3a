<template>
  <div class="chat-test">
    <h2>聊天功能测试</h2>
    
    <div class="test-section">
      <h3>WebSocket连接状态</h3>
      <p>状态: {{ connectionStatus }}</p>
      <p>是否连接: {{ isConnected ? '是' : '否' }}</p>
      <el-button @click="initWebSocket" :disabled="isConnected">初始化连接</el-button>
      <el-button @click="closeWebSocket" :disabled="!isConnected">关闭连接</el-button>
    </div>

    <div class="test-section">
      <h3>数据库测试</h3>
      <el-button @click="testDatabase">测试数据库</el-button>
      <el-button @click="clearDatabase">清空数据库</el-button>
    </div>

    <div class="test-section">
      <h3>用户信息测试</h3>
      <p>当前用户ID: {{ currentUserId }}</p>
      <p>Token中的用户ID: {{ tokenUserId }}</p>
      <el-button @click="checkUserInfo">检查用户信息</el-button>
    </div>

    <div class="test-section">
      <h3>消息发送测试</h3>
      <el-input v-model="testMessage" placeholder="输入测试消息" style="width: 300px; margin-right: 10px;"></el-input>
      <el-button @click="sendTestMessage" :disabled="!isConnected">发送群消息</el-button>
    </div>

    <div class="test-section">
      <h3>聊天对话框</h3>
      <el-button @click="openChatDialog">打开聊天对话框</el-button>
    </div>

    <div class="test-section">
      <h3>日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
      <el-button @click="clearLogs">清空日志</el-button>
    </div>

    <!-- 聊天对话框 -->
    <ChatDialog v-model="showChatDialog" :user="currentUser" @close="showChatDialog = false" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useWebSocketStore } from '@/pinia/modules/websocket.js'
import { sendMessage } from '@/utils/chatService.js'
import { openDb, addTabItem, getChatMessages, clearAllData } from '@/utils/db.js'
import ChatDialog from '../ChatDialog.vue'
import { ElMessage } from 'element-plus'

const webSocketStore = useWebSocketStore()
const connectionStatus = computed(() => webSocketStore.connectionStatus)
const isConnected = computed(() => webSocketStore.isConnected)

const testMessage = ref('这是一条测试消息')
const showChatDialog = ref(false)
const logs = ref([])

const currentUser = ref({
  id: 10003,
  name: '测试用户',
  avatar: ''
})

// 用户信息
const currentUserId = ref(localStorage.getItem('userId') || '未知')
const tokenUserId = ref('未解析')

// 添加日志
const addLog = (message) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 初始化WebSocket
const initWebSocket = async () => {
  try {
    addLog('开始初始化WebSocket连接...')
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJEYXRhIjp7ImlwaG9uZV9jb2RlIjoiIiwidXNlcl9pZCI6MTAwMDMsImlzX2xvZ2luIjp0cnVlfSwiZXhwIjoxNzU2NDM3ODQ3LCJuYmYiOjE3NTM4NDU4NDcsImlhdCI6MTc1Mzg0NTg0N30.OflBjUMWWU9_eG_349GkCtSqqKOevZpsm_f_mW6YClY'
    
    await webSocketStore.initConnection(token, true)
    addLog('WebSocket连接成功')
    ElMessage.success('WebSocket连接成功')
  } catch (error) {
    addLog(`WebSocket连接失败: ${error.message}`)
    ElMessage.error('WebSocket连接失败')
  }
}

// 关闭WebSocket
const closeWebSocket = () => {
  webSocketStore.closeConnection()
  addLog('WebSocket连接已关闭')
}

// 测试数据库
const testDatabase = async () => {
  try {
    addLog('开始测试数据库...')
    
    // 打开数据库
    await openDb()
    addLog('数据库打开成功')
    
    // 添加测试消息
    const testMsg = {
      id: Date.now(),
      typecode: 2,
      typecode2: 0,
      toid: 1,
      fromid: 10003,
      chatid: '1',
      t: new Date().toISOString(),
      msg: '数据库测试消息',
      isRedRead: 0,
      idDel: 0,
      senderAvatar: '',
      senderNickname: '测试用户',
      avatar: '',
      nickname: '官方群',
      lastMessage: '数据库测试消息',
      timestamp: new Date().getTime(),
      unreadCount: 1
    }
    
    await addTabItem(testMsg)
    addLog('测试消息添加成功')
    
    // 查询消息
    const messages = await getChatMessages('1', 1, 10)
    addLog(`查询到 ${messages.length} 条消息`)
    
    ElMessage.success('数据库测试成功')
  } catch (error) {
    addLog(`数据库测试失败: ${error.message}`)
    ElMessage.error('数据库测试失败')
  }
}

// 清空数据库
const clearDatabase = async () => {
  try {
    await clearAllData()
    addLog('数据库已清空')
    ElMessage.success('数据库已清空')
  } catch (error) {
    addLog(`清空数据库失败: ${error.message}`)
    ElMessage.error('清空数据库失败')
  }
}

// 发送测试消息
const sendTestMessage = async () => {
  try {
    if (!testMessage.value.trim()) {
      ElMessage.warning('请输入测试消息')
      return
    }
    
    addLog('开始发送测试消息...')
    
    const messageParams = {
      fromid: 10003,
      toId: 1,
      msg: testMessage.value.trim(),
      typecode: 2, // 群消息
      typecode2: 0, // 文本消息
      groupID: 1
    }
    
    const result = await sendMessage(messageParams)
    addLog(`消息发送成功: ${JSON.stringify(result)}`)
    ElMessage.success('消息发送成功')
    
    testMessage.value = ''
  } catch (error) {
    addLog(`消息发送失败: ${error.message}`)
    ElMessage.error('消息发送失败')
  }
}

// 打开聊天对话框
const openChatDialog = () => {
  showChatDialog.value = true
}

// 检查用户信息
const checkUserInfo = () => {
  // 更新当前用户ID
  currentUserId.value = localStorage.getItem('userId') || '未知'

  // 解析token中的用户ID
  try {
    const token = localStorage.getItem('token')
    if (token) {
      const payload = JSON.parse(atob(token.split('.')[1]))
      if (payload.Data && payload.Data.user_id) {
        tokenUserId.value = payload.Data.user_id.toString()
      } else {
        tokenUserId.value = '未找到'
      }
    } else {
      tokenUserId.value = '无token'
    }
  } catch (error) {
    tokenUserId.value = '解析失败'
    addLog(`Token解析失败: ${error.message}`)
  }

  addLog(`当前用户ID: ${currentUserId.value}`)
  addLog(`Token中的用户ID: ${tokenUserId.value}`)
}

// 清空日志
const clearLogs = () => {
  logs.value = []
}

onMounted(() => {
  addLog('聊天测试页面已加载')
  checkUserInfo()
})
</script>

<style lang="scss" scoped>
.chat-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fff;

  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #303133;
  }

  p {
    margin: 5px 0;
    color: #606266;
  }
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background: #f5f7fa;
  margin-bottom: 10px;

  .log-item {
    font-family: monospace;
    font-size: 12px;
    line-height: 1.4;
    color: #606266;
    margin-bottom: 2px;
  }
}
</style>
