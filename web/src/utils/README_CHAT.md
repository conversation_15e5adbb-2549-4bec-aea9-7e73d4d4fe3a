# 聊天功能集成说明

本文档说明了在 `web/` 目录下实现的 WebSocket 消息处理、IndexedDB 数据库存储和 Pinia 状态管理功能，该功能与 `chat-app/utils/connectionService.js` 和 `chat-app/utils/db.js` 的功能保持一致。

## 功能概述

### 已实现的功能

1. **IndexedDB 数据库操作** (`web/src/utils/db.js`)
   - 消息存储和检索
   - 聊天列表管理
   - 消息已读状态管理
   - 分页查询支持

2. **WebSocket 连接服务** (`web/src/utils/connectionService.js`)
   - 消息接收处理
   - 消息解密
   - 用户信息获取
   - 数据库存储集成

3. **WebSocket 管理器增强** (`web/src/utils/websocket.js`)
   - 集成连接服务初始化
   - 自动重连机制
   - 持久连接支持

4. **Pinia 状态管理增强** (`web/src/pinia/modules/websocket.js`)
   - 数据库操作方法
   - 消息状态管理
   - 未读消息计数

5. **消息发送功能** (`web/src/utils/chatService.js`)
   - 消息加密
   - API 调用
   - 参数验证

## 文件结构

```
web/src/utils/
├── db.js                    # IndexedDB 数据库操作
├── connectionService.js     # WebSocket 消息处理服务
├── websocket.js            # WebSocket 连接管理器
├── chatService.js          # 消息发送服务
├── chatExample.js          # 使用示例
├── chatTest.js             # 功能测试
└── README_CHAT.md          # 本说明文档

web/src/pinia/modules/
└── websocket.js            # WebSocket 状态管理
```

## 使用方法

### 1. 初始化聊天功能

```javascript
import { useWebSocketStore } from '@/pinia/modules/websocket.js'

const webSocketStore = useWebSocketStore()

// 初始化 WebSocket 连接
const token = 'your-user-token'
await webSocketStore.initConnection(token, true)
```

### 2. 发送消息

```javascript
import { sendMessage } from '@/utils/chatService.js'

// 发送私聊消息
const privateMessage = {
  fromid: 10003,           // 发送者ID
  toId: 10001,            // 接收者ID
  msg: '你好',             // 消息内容
  typecode: 1,            // 1-私聊, 2-群聊, 3-通知
  typecode2: 0            // 0-文本, 1-音频, 2-图片, 3-视频
}

await sendMessage(privateMessage)

// 发送群聊消息
const groupMessage = {
  fromid: 10003,
  toId: 20001,
  msg: '大家好',
  typecode: 2,
  typecode2: 0,
  groupID: 20001          // 群组ID
}

await sendMessage(groupMessage)
```

### 3. 获取聊天数据

```javascript
const webSocketStore = useWebSocketStore()

// 获取聊天列表
const chatList = await webSocketStore.loadChatList()

// 获取聊天消息（分页）
const messages = await webSocketStore.loadChatMessages('chatId', 1, 20)

// 标记消息为已读
await webSocketStore.markChatAsRead('chatId')
```

### 4. 监听 WebSocket 状态

```javascript
const webSocketStore = useWebSocketStore()

// 连接状态
console.log('连接状态:', webSocketStore.connectionStatus)
console.log('是否已连接:', webSocketStore.isConnected)

// 消息状态
console.log('未读消息数:', webSocketStore.unreadCount)
console.log('最新消息:', webSocketStore.lastMessage)
console.log('消息历史:', webSocketStore.messageHistory)
```

## 数据库字段说明

### 消息对象结构

```javascript
{
  id: Number,              // 消息ID
  typecode: Number,        // 消息类型：1-私聊, 2-群聊, 3-通知
  typecode2: Number,       // 内容类型：0-文本, 1-音频, 2-图片, 3-视频, 9-语音通话
  toid: String,           // 接收者ID
  fromid: String,         // 发送者ID
  chatid: String,         // 聊天对象ID
  t: String,              // 时间戳 (ISO格式)
  msg: String,            // 消息内容
  isRedRead: Number,      // 是否已读：0-未读, 1-已读
  idDel: Number,          // 是否删除：0-未删除, 1-已删除
  senderAvatar: String,   // 发送者头像
  senderNickname: String, // 发送者昵称
  avatar: String,         // 头像
  nickname: String,       // 昵称
  lastMessage: String,    // 最后一条消息
  timestamp: Number,      // 时间戳
  unreadCount: Number     // 未读消息数
}
```

## 测试功能

### 运行测试

```javascript
import { runAllTests, quickTest } from '@/utils/chatTest.js'

// 快速测试
await quickTest()

// 运行所有测试
await runAllTests()
```

### 浏览器控制台测试

```javascript
// 在浏览器控制台中直接使用
window.chatTest.quickTest()
window.chatTest.runAllTests()
window.chatTest.testDatabase()
window.chatTest.testWebSocket()
```

## 注意事项

1. **用户ID设置**：确保在使用前设置用户ID到 localStorage
   ```javascript
   localStorage.setItem('userId', '10003')
   ```

2. **Token配置**：使用有效的JWT token进行WebSocket连接

3. **消息加密**：消息内容会自动进行AES加密/解密

4. **数据存储**：使用IndexedDB在浏览器本地存储数据

5. **自动重连**：支持网络断开后的自动重连

6. **持久连接**：支持页面刷新后的连接恢复

## API 参考

### 数据库操作 (`db.js`)

- `openDb()` - 打开数据库
- `addTabItem(item)` - 添加消息
- `getChatMessages(chatId, page, size)` - 获取聊天消息
- `getChatList()` - 获取聊天列表
- `markMessagesAsRead(chatId)` - 标记消息为已读

### WebSocket 操作 (`websocket.js`)

- `connect(token, persistent)` - 连接WebSocket
- `send(data)` - 发送消息
- `addMessageHandler(type, handler)` - 添加消息处理器
- `close()` - 关闭连接

### Pinia Store 方法 (`websocket.js`)

- `initConnection(token, persistent)` - 初始化连接
- `sendMessage(data)` - 发送消息
- `loadChatList()` - 加载聊天列表
- `loadChatMessages(chatId, page, size)` - 加载聊天消息
- `markChatAsRead(chatId)` - 标记聊天为已读

## 故障排除

1. **连接失败**：检查token是否有效，网络是否正常
2. **消息不显示**：检查数据库是否正常打开，用户ID是否设置
3. **加密错误**：检查decrypt.js文件是否正确导入
4. **状态不更新**：检查Pinia store是否正确初始化

## 与chat-app的对应关系

| chat-app | web | 功能 |
|----------|-----|------|
| `utils/db.js` | `utils/db.js` | 数据库操作（SQLite → IndexedDB） |
| `utils/connectionService.js` | `utils/connectionService.js` | WebSocket消息处理 |
| - | `pinia/modules/websocket.js` | 状态管理（替代uni.$emit） |
| - | `utils/chatService.js` | 消息发送服务 |

功能完全对等，只是存储方式从SQLite改为IndexedDB，事件系统从uni.$emit改为Pinia状态管理。
