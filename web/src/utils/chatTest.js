/**
 * 聊天功能测试文件
 * 用于测试 WebSocket 消息处理、数据库存储和消息发送功能
 */

import { openDb, addTabItem, getChatMessages, getChatList, markMessagesAsRead } from './db.js'
import { sendMessage } from './chatService.js'
import { useWebSocketStore } from '@/pinia/modules/websocket.js'

/**
 * 测试数据库功能
 */
tools.vue:208 打开聊天框
MessagePanel.vue:85 加载聊天消息，chatId: 1
hook.js:608 获取聊天消息处理错误: NotFoundError: Failed to execute 'transaction' on 'IDBDatabase': One of the specified object stores was not found.
    at db.js:173:30
    at new Promise (<anonymous>)
    at getChatMessages (db.js:162:10)
    at loadMessages (MessagePanel.vue:88:30)
    at watch.immediate (MessagePanel.vue:341:5)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6251:47)
    at job (reactivity.esm-bundler.js:1831:18)
    at watch (reactivity.esm-bundler.js:1866:7)
overrideMethod @ hook.js:608
(anonymous) @ db.js:200
getChatMessages @ db.js:162
loadMessages @ MessagePanel.vue:88
watch.immediate @ MessagePanel.vue:341
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
setup @ MessagePanel.vue:339
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5671
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
handleOpenChatDialog @ index.vue:135
(anonymous) @ index.ts:109
emit @ index.ts:108
handleMessage @ tools.vue:210
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
hook.js:608 ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.
For more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes

    at debugWarn (error.ts:13:37)
    at watch.immediate (index.ts:6:7)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6251:47)
    at job (reactivity.esm-bundler.js:1831:18)
    at watch (reactivity.esm-bundler.js:1866:7)
    at doWatch (runtime-core.esm-bundler.js:6279:23)
    at watch2 (runtime-core.esm-bundler.js:6212:10)
    at useDeprecated (index.ts:4:3)
overrideMethod @ hook.js:608
debugWarn @ error.ts:14
watch.immediate @ index.ts:6
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
useDeprecated @ index.ts:4
useButton @ use-button.ts:11
setup @ button.vue:56
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5671
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
handleOpenChatDialog @ index.vue:135
(anonymous) @ index.ts:109
emit @ index.ts:108
handleMessage @ tools.vue:210
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
hook.js:608 ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.
For more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes

    at debugWarn (error.ts:13:37)
    at watch.immediate (index.ts:6:7)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6251:47)
    at job (reactivity.esm-bundler.js:1831:18)
    at watch (reactivity.esm-bundler.js:1866:7)
    at doWatch (runtime-core.esm-bundler.js:6279:23)
    at watch2 (runtime-core.esm-bundler.js:6212:10)
    at useDeprecated (index.ts:4:3)
overrideMethod @ hook.js:608
debugWarn @ error.ts:14
watch.immediate @ index.ts:6
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
useDeprecated @ index.ts:4
useButton @ use-button.ts:11
setup @ button.vue:56
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5671
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
handleOpenChatDialog @ index.vue:135
(anonymous) @ index.ts:109
emit @ index.ts:108
handleMessage @ tools.vue:210
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
hook.js:608 ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.
For more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes

    at debugWarn (error.ts:13:37)
    at watch.immediate (index.ts:6:7)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6251:47)
    at job (reactivity.esm-bundler.js:1831:18)
    at watch (reactivity.esm-bundler.js:1866:7)
    at doWatch (runtime-core.esm-bundler.js:6279:23)
    at watch2 (runtime-core.esm-bundler.js:6212:10)
    at useDeprecated (index.ts:4:3)
overrideMethod @ hook.js:608
debugWarn @ error.ts:14
watch.immediate @ index.ts:6
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
useDeprecated @ index.ts:4
useButton @ use-button.ts:11
setup @ button.vue:56
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5671
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
handleOpenChatDialog @ index.vue:135
(anonymous) @ index.ts:109
emit @ index.ts:108
handleMessage @ tools.vue:210
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
hook.js:608 ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.
For more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes

    at debugWarn (error.ts:13:37)
    at watch.immediate (index.ts:6:7)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6251:47)
    at job (reactivity.esm-bundler.js:1831:18)
    at watch (reactivity.esm-bundler.js:1866:7)
    at doWatch (runtime-core.esm-bundler.js:6279:23)
    at watch2 (runtime-core.esm-bundler.js:6212:10)
    at useDeprecated (index.ts:4:3)
overrideMethod @ hook.js:608
debugWarn @ error.ts:14
watch.immediate @ index.ts:6
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
useDeprecated @ index.ts:4
useButton @ use-button.ts:11
setup @ button.vue:56
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5671
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
handleOpenChatDialog @ index.vue:135
(anonymous) @ index.ts:109
emit @ index.ts:108
handleMessage @ tools.vue:210
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
MessagePanel.vue:85 加载聊天消息，chatId: 1
hook.js:608 获取聊天消息处理错误: NotFoundError: Failed to execute 'transaction' on 'IDBDatabase': One of the specified object stores was not found.
    at db.js:173:30
    at new Promise (<anonymous>)
    at getChatMessages (db.js:162:10)
    at loadMessages (MessagePanel.vue:88:30)
    at MessagePanel.vue:330:3
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
overrideMethod @ hook.js:608
(anonymous) @ db.js:200
getChatMessages @ db.js:162
loadMessages @ MessagePanel.vue:88
(anonymous) @ MessagePanel.vue:330
(anonymous) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
handleOpenChatDialog @ index.vue:135
(anonymous) @ index.ts:109
emit @ index.ts:108
handleMessage @ tools.vue:210
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
hook.js:608 加载消息失败: NotFoundError: Failed to execute 'transaction' on 'IDBDatabase': One of the specified object stores was not found.
    at db.js:173:30
    at new Promise (<anonymous>)
    at getChatMessages (db.js:162:10)
    at loadMessages (MessagePanel.vue:88:30)
    at watch.immediate (MessagePanel.vue:341:5)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6251:47)
    at job (reactivity.esm-bundler.js:1831:18)
    at watch (reactivity.esm-bundler.js:1866:7)
overrideMethod @ hook.js:608
loadMessages @ MessagePanel.vue:125
await in loadMessages
watch.immediate @ MessagePanel.vue:341
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
setup @ MessagePanel.vue:339
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5671
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
handleOpenChatDialog @ index.vue:135
(anonymous) @ index.ts:109
emit @ index.ts:108
handleMessage @ tools.vue:210
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
hook.js:608 加载消息失败: NotFoundError: Failed to execute 'transaction' on 'IDBDatabase': One of the specified object stores was not found.
    at db.js:173:30
    at new Promise (<anonymous>)
    at getChatMessages (db.js:162:10)
    at loadMessages (MessagePanel.vue:88:30)
    at MessagePanel.vue:330:3
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
overrideMethod @ hook.js:608
loadMessages @ MessagePanel.vue:125
await in loadMessages
(anonymous) @ MessagePanel.vue:330
(anonymous) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
handleOpenChatDialog @ index.vue:135
(anonymous) @ index.ts:109
emit @ index.ts:108
handleMessage @ tools.vue:210
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
groupMemberUtils.js:49 所有用户数据: 7 群成员ID: Reactive<Array(7)>
groupMemberUtils.js:84 群成员数据: (7) [{…}, {…}, {…}, {…}, {…}, {…}, {…}]
hook.js:608 ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.
For more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes

    at debugWarn (error.ts:13:37)
    at watch.immediate (index.ts:6:7)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6251:47)
    at job (reactivity.esm-bundler.js:1831:18)
    at watch (reactivity.esm-bundler.js:1866:7)
    at doWatch (runtime-core.esm-bundler.js:6279:23)
    at watch2 (runtime-core.esm-bundler.js:6212:10)
    at useDeprecated (index.ts:4:3)
overrideMethod @ hook.js:608
debugWarn @ error.ts:14
watch.immediate @ index.ts:6
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
useDeprecated @ index.ts:4
useButton @ use-button.ts:11
setup @ button.vue:56
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
patchBlockChildren @ runtime-core.esm-bundler.js:5085
processFragment @ runtime-core.esm-bundler.js:5161
patch @ runtime-core.esm-bundler.js:4703
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
fetchGroupMembers @ GroupUserPanel.vue:145
await in fetchGroupMembers
watch.immediate @ GroupUserPanel.vue:163
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
setup @ GroupUserPanel.vue:161
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5671
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
handleOpenChatDialog @ index.vue:135
(anonymous) @ index.ts:109
emit @ index.ts:108
handleMessage @ tools.vue:210
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
hook.js:608 ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.
For more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes

    at debugWarn (error.ts:13:37)
    at watch.immediate (index.ts:6:7)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6251:47)
    at job (reactivity.esm-bundler.js:1831:18)
    at watch (reactivity.esm-bundler.js:1866:7)
    at doWatch (runtime-core.esm-bundler.js:6279:23)
    at watch2 (runtime-core.esm-bundler.js:6212:10)
    at useDeprecated (index.ts:4:3)
overrideMethod @ hook.js:608
debugWarn @ error.ts:14
watch.immediate @ index.ts:6
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
useDeprecated @ index.ts:4
useButton @ use-button.ts:11
setup @ button.vue:56
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
patchBlockChildren @ runtime-core.esm-bundler.js:5085
processFragment @ runtime-core.esm-bundler.js:5161
patch @ runtime-core.esm-bundler.js:4703
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
fetchGroupMembers @ GroupUserPanel.vue:145
await in fetchGroupMembers
watch.immediate @ GroupUserPanel.vue:163
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
setup @ GroupUserPanel.vue:161
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5671
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
handleOpenChatDialog @ index.vue:135
(anonymous) @ index.ts:109
emit @ index.ts:108
handleMessage @ tools.vue:210
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
hook.js:608 ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.
For more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes

    at debugWarn (error.ts:13:37)
    at watch.immediate (index.ts:6:7)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6251:47)
    at job (reactivity.esm-bundler.js:1831:18)
    at watch (reactivity.esm-bundler.js:1866:7)
    at doWatch (runtime-core.esm-bundler.js:6279:23)
    at watch2 (runtime-core.esm-bundler.js:6212:10)
    at useDeprecated (index.ts:4:3)
overrideMethod @ hook.js:608
debugWarn @ error.ts:14
watch.immediate @ index.ts:6
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
useDeprecated @ index.ts:4
useButton @ use-button.ts:11
setup @ button.vue:56
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
patchBlockChildren @ runtime-core.esm-bundler.js:5085
processFragment @ runtime-core.esm-bundler.js:5161
patch @ runtime-core.esm-bundler.js:4703
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
fetchGroupMembers @ GroupUserPanel.vue:145
await in fetchGroupMembers
watch.immediate @ GroupUserPanel.vue:163
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
setup @ GroupUserPanel.vue:161
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5671
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
handleOpenChatDialog @ index.vue:135
(anonymous) @ index.ts:109
emit @ index.ts:108
handleMessage @ tools.vue:210
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
hook.js:608 ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.
For more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes

    at debugWarn (error.ts:13:37)
    at watch.immediate (index.ts:6:7)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6251:47)
    at job (reactivity.esm-bundler.js:1831:18)
    at watch (reactivity.esm-bundler.js:1866:7)
    at doWatch (runtime-core.esm-bundler.js:6279:23)
    at watch2 (runtime-core.esm-bundler.js:6212:10)
    at useDeprecated (index.ts:4:3)
overrideMethod @ hook.js:608
debugWarn @ error.ts:14
watch.immediate @ index.ts:6
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
useDeprecated @ index.ts:4
useButton @ use-button.ts:11
setup @ button.vue:56
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
patchBlockChildren @ runtime-core.esm-bundler.js:5085
processFragment @ runtime-core.esm-bundler.js:5161
patch @ runtime-core.esm-bundler.js:4703
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
fetchGroupMembers @ GroupUserPanel.vue:145
await in fetchGroupMembers
watch.immediate @ GroupUserPanel.vue:163
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
setup @ GroupUserPanel.vue:161
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5671
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
handleOpenChatDialog @ index.vue:135
(anonymous) @ index.ts:109
emit @ index.ts:108
handleMessage @ tools.vue:210
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
hook.js:608 ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.
For more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes

    at debugWarn (error.ts:13:37)
    at watch.immediate (index.ts:6:7)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6251:47)
    at job (reactivity.esm-bundler.js:1831:18)
    at watch (reactivity.esm-bundler.js:1866:7)
    at doWatch (runtime-core.esm-bundler.js:6279:23)
    at watch2 (runtime-core.esm-bundler.js:6212:10)
    at useDeprecated (index.ts:4:3)
overrideMethod @ hook.js:608
debugWarn @ error.ts:14
watch.immediate @ index.ts:6
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
useDeprecated @ index.ts:4
useButton @ use-button.ts:11
setup @ button.vue:56
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
patchBlockChildren @ runtime-core.esm-bundler.js:5085
processFragment @ runtime-core.esm-bundler.js:5161
patch @ runtime-core.esm-bundler.js:4703
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
fetchGroupMembers @ GroupUserPanel.vue:145
await in fetchGroupMembers
watch.immediate @ GroupUserPanel.vue:163
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
setup @ GroupUserPanel.vue:161
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5671
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
handleOpenChatDialog @ index.vue:135
(anonymous) @ index.ts:109
emit @ index.ts:108
handleMessage @ tools.vue:210
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
hook.js:608 ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.
For more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes

    at debugWarn (error.ts:13:37)
    at watch.immediate (index.ts:6:7)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6251:47)
    at job (reactivity.esm-bundler.js:1831:18)
    at watch (reactivity.esm-bundler.js:1866:7)
    at doWatch (runtime-core.esm-bundler.js:6279:23)
    at watch2 (runtime-core.esm-bundler.js:6212:10)
    at useDeprecated (index.ts:4:3)
overrideMethod @ hook.js:608
debugWarn @ error.ts:14
watch.immediate @ index.ts:6
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
useDeprecated @ index.ts:4
useButton @ use-button.ts:11
setup @ button.vue:56
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
patchBlockChildren @ runtime-core.esm-bundler.js:5085
processFragment @ runtime-core.esm-bundler.js:5161
patch @ runtime-core.esm-bundler.js:4703
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
fetchGroupMembers @ GroupUserPanel.vue:145
await in fetchGroupMembers
watch.immediate @ GroupUserPanel.vue:163
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
setup @ GroupUserPanel.vue:161
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5671
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
handleOpenChatDialog @ index.vue:135
(anonymous) @ index.ts:109
emit @ index.ts:108
handleMessage @ tools.vue:210
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
hook.js:608 ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.
For more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes

    at debugWarn (error.ts:13:37)
    at watch.immediate (index.ts:6:7)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6251:47)
    at job (reactivity.esm-bundler.js:1831:18)
    at watch (reactivity.esm-bundler.js:1866:7)
    at doWatch (runtime-core.esm-bundler.js:6279:23)
    at watch2 (runtime-core.esm-bundler.js:6212:10)
    at useDeprecated (index.ts:4:3)
overrideMethod @ hook.js:608
debugWarn @ error.ts:14
watch.immediate @ index.ts:6
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
useDeprecated @ index.ts:4
useButton @ use-button.ts:11
setup @ button.vue:56
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
patchBlockChildren @ runtime-core.esm-bundler.js:5085
processFragment @ runtime-core.esm-bundler.js:5161
patch @ runtime-core.esm-bundler.js:4703
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
fetchGroupMembers @ GroupUserPanel.vue:145
await in fetchGroupMembers
watch.immediate @ GroupUserPanel.vue:163
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6251
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6279
watch2 @ runtime-core.esm-bundler.js:6212
setup @ GroupUserPanel.vue:161
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5671
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
processFragment @ runtime-core.esm-bundler.js:5187
patch @ runtime-core.esm-bundler.js:4703
patchKeyedChildren @ runtime-core.esm-bundler.js:5629
patchChildren @ runtime-core.esm-bundler.js:5543
patchElement @ runtime-core.esm-bundler.js:5016
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
handleOpenChatDialog @ index.vue:135
(anonymous) @ index.ts:109
emit @ index.ts:108
handleMessage @ tools.vue:210
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
groupMemberUtils.js:49 所有用户数据: 7 群成员ID: Reactive<Array(7)>
groupMemberUtils.js:84 群成员数据: (7) [{…}, {…}, {…}, {…}, {…}, {…}, {…}]
websocket.js:156 发送WebSocket消息: {type: 0}

export const testDatabase = async () => {
  console.log('=== 开始测试数据库功能 ===')
  
  try {
    // 1. 测试打开数据库
    console.log('1. 测试打开数据库...')
    await openDb()
    console.log('✓ 数据库打开成功')

    // 2. 测试添加消息
    console.log('2. 测试添加消息...')
    const testMessage = {
      id: Date.now(),
      typecode: 1,
      typecode2: 0,
      toid: '10001',
      fromid: '10002',
      chatid: '10001',
      t: new Date().toISOString(),
      msg: '这是一条测试消息',
      isRedRead: 0,
      idDel: 0,
      senderAvatar: '/static/My/avatar.jpg',
      senderNickname: '测试用户',
      avatar: '/static/My/avatar.jpg',
      nickname: '测试用户',
      lastMessage: '这是一条测试消息',
      timestamp: Date.now(),
      unreadCount: 1
    }

    const savedMessage = await addTabItem(testMessage)
    console.log('✓ 消息添加成功:', savedMessage)

    // 3. 测试获取聊天消息
    console.log('3. 测试获取聊天消息...')
    const messages = await getChatMessages('10001', 1, 10)
    console.log('✓ 获取聊天消息成功:', messages.length, '条')

    // 4. 测试获取聊天列表
    console.log('4. 测试获取聊天列表...')
    const chatList = await getChatList()
    console.log('✓ 获取聊天列表成功:', chatList.length, '个')

    // 5. 测试标记消息为已读
    console.log('5. 测试标记消息为已读...')
    await markMessagesAsRead('10001')
    console.log('✓ 标记消息为已读成功')

    console.log('=== 数据库功能测试完成 ===')
    return true

  } catch (error) {
    console.error('✗ 数据库功能测试失败:', error)
    return false
  }
}

/**
 * 测试 WebSocket 连接
 */
export const testWebSocket = async () => {
  console.log('=== 开始测试 WebSocket 连接 ===')
  
  try {
    const webSocketStore = useWebSocketStore()
    
    // 1. 测试连接
    console.log('1. 测试 WebSocket 连接...')
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJEYXRhIjp7ImlwaG9uZV9jb2RlIjoiIiwidXNlcl9pZCI6MTAwMDMsImlzX2xvZ2luIjp0cnVlfSwiZXhwIjoxNzU2NDM3ODQ3LCJuYmYiOjE3NTM4NDU4NDcsImlhdCI6MTc1Mzg0NTg0N30.OflBjUMWWU9_eG_349GkCtSqqKOevZpsm_f_mW6YClY'
    
    await webSocketStore.initConnection(token, true)
    console.log('✓ WebSocket 连接成功')

    // 2. 测试连接状态
    console.log('2. 检查连接状态...')
    console.log('连接状态:', webSocketStore.connectionStatus)
    console.log('是否已连接:', webSocketStore.isConnected)

    // 3. 等待一段时间以接收消息
    console.log('3. 等待接收消息...')
    await new Promise(resolve => setTimeout(resolve, 3000))

    console.log('未读消息数:', webSocketStore.unreadCount)
    console.log('最新消息:', webSocketStore.lastMessage)
    console.log('消息历史:', webSocketStore.messageHistory.length, '条')

    console.log('=== WebSocket 连接测试完成 ===')
    return true

  } catch (error) {
    console.error('✗ WebSocket 连接测试失败:', error)
    return false
  }
}

/**
 * 测试消息发送
 */
export const testSendMessage = async () => {
  console.log('=== 开始测试消息发送 ===')
  
  try {
    // 测试发送私聊消息
    console.log('1. 测试发送私聊消息...')
    const privateMessage = {
      fromid: 10003,
      toId: 10001,
      msg: '这是一条测试私聊消息',
      typecode: 1,
      typecode2: 0
    }

    const result1 = await sendMessage(privateMessage)
    console.log('✓ 私聊消息发送成功:', result1)

    // 测试发送群聊消息
    console.log('2. 测试发送群聊消息...')
    const groupMessage = {
      fromid: 10003,
      toId: 20001,
      msg: '这是一条测试群聊消息',
      typecode: 2,
      typecode2: 0,
      groupID: 20001
    }

    const result2 = await sendMessage(groupMessage)
    console.log('✓ 群聊消息发送成功:', result2)

    console.log('=== 消息发送测试完成 ===')
    return true

  } catch (error) {
    console.error('✗ 消息发送测试失败:', error)
    return false
  }
}

/**
 * 测试 Pinia Store 数据库方法
 */
export const testStoreDatabase = async () => {
  console.log('=== 开始测试 Pinia Store 数据库方法 ===')
  
  try {
    const webSocketStore = useWebSocketStore()

    // 1. 测试加载聊天列表
    console.log('1. 测试加载聊天列表...')
    const chatList = await webSocketStore.loadChatList()
    console.log('✓ 聊天列表加载成功:', chatList.length, '个')

    if (chatList.length > 0) {
      const firstChat = chatList[0]
      
      // 2. 测试加载聊天消息
      console.log('2. 测试加载聊天消息...')
      const messages = await webSocketStore.loadChatMessages(firstChat.chatId, 1, 10)
      console.log('✓ 聊天消息加载成功:', messages.length, '条')

      // 3. 测试标记消息为已读
      console.log('3. 测试标记消息为已读...')
      const success = await webSocketStore.markChatAsRead(firstChat.chatId)
      console.log('✓ 标记消息为已读:', success ? '成功' : '失败')
    }

    console.log('=== Pinia Store 数据库方法测试完成 ===')
    return true

  } catch (error) {
    console.error('✗ Pinia Store 数据库方法测试失败:', error)
    return false
  }
}

/**
 * 运行所有测试
 */
export const runAllTests = async () => {
  console.log('🚀 开始运行所有测试...')
  
  const results = {
    database: false,
    webSocket: false,
    sendMessage: false,
    storeDatabase: false
  }

  // 测试数据库功能
  results.database = await testDatabase()
  
  // 测试 WebSocket 连接
  results.webSocket = await testWebSocket()
  
  // 测试消息发送
  results.sendMessage = await testSendMessage()
  
  // 测试 Pinia Store 数据库方法
  results.storeDatabase = await testStoreDatabase()

  // 输出测试结果
  console.log('📊 测试结果汇总:')
  console.log('数据库功能:', results.database ? '✓ 通过' : '✗ 失败')
  console.log('WebSocket 连接:', results.webSocket ? '✓ 通过' : '✗ 失败')
  console.log('消息发送:', results.sendMessage ? '✓ 通过' : '✗ 失败')
  console.log('Store 数据库方法:', results.storeDatabase ? '✓ 通过' : '✗ 失败')

  const allPassed = Object.values(results).every(result => result === true)
  console.log('总体结果:', allPassed ? '🎉 所有测试通过' : '❌ 部分测试失败')

  return results
}

/**
 * 快速测试函数 - 在浏览器控制台中使用
 */
export const quickTest = async () => {
  console.log('🔧 快速测试聊天功能...')
  
  try {
    // 设置用户ID到localStorage
    localStorage.setItem('userId', '10003')
    
    // 运行数据库测试
    await testDatabase()
    
    console.log('✅ 快速测试完成')
  } catch (error) {
    console.error('❌ 快速测试失败:', error)
  }
}

// 导出到全局对象，方便在控制台中使用
if (typeof window !== 'undefined') {
  window.chatTest = {
    testDatabase,
    testWebSocket,
    testSendMessage,
    testStoreDatabase,
    runAllTests,
    quickTest
  }
  
  console.log('💡 聊天测试功能已加载，可在控制台使用：')
  console.log('- window.chatTest.quickTest() - 快速测试')
  console.log('- window.chatTest.runAllTests() - 运行所有测试')
  console.log('- window.chatTest.testDatabase() - 测试数据库')
  console.log('- window.chatTest.testWebSocket() - 测试WebSocket')
}
