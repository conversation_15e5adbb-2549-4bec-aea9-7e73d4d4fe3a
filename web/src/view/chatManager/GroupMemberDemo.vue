<template>
  <div class="group-member-demo">
    <div class="demo-header">
      <h1>群成员功能演示</h1>
      <p>这个演示展示了如何根据群组的 FromID 数组获取和展示群成员信息</p>
    </div>

    <div class="demo-content">
      <!-- 步骤说明 -->
      <div class="steps">
        <el-steps :active="currentStep" finish-status="success">
          <el-step title="获取群组数据" description="从API获取群组列表"></el-step>
          <el-step title="解析成员ID" description="提取FromID数组"></el-step>
          <el-step title="获取用户信息" description="根据ID获取用户详情"></el-step>
          <el-step title="展示成员列表" description="显示群成员面板"></el-step>
        </el-steps>
      </div>

      <!-- 操作按钮 -->
      <div class="actions">
        <el-button @click="startDemo" type="primary" :loading="loading">
          开始演示
        </el-button>
        <el-button @click="resetDemo" :disabled="loading">
          重置演示
        </el-button>
      </div>

      <!-- 数据展示 -->
      <div class="data-display" v-if="currentStep > 0">
        <!-- 步骤1: 群组数据 -->
        <div v-if="currentStep >= 1" class="step-data">
          <h3>步骤1: 群组数据</h3>
          <el-card>
            <div v-if="groupData">
              <p><strong>群组ID:</strong> {{ groupData.ID }}</p>
              <p><strong>群组名称:</strong> {{ groupData.GroupName }}</p>
              <p><strong>群主ID:</strong> {{ groupData.GroupHave }}</p>
              <p><strong>管理员:</strong> {{ groupData.Admins?.join(', ') || '无' }}</p>
              <p><strong>成员ID数组 (FromID):</strong></p>
              <div class="member-ids">
                <el-tag v-for="id in groupData.FromID" :key="id" class="id-tag">
                  {{ id }}
                </el-tag>
              </div>
            </div>
            <el-skeleton v-else :rows="4" animated />
          </el-card>
        </div>

        <!-- 步骤2: 成员ID解析 -->
        <div v-if="currentStep >= 2" class="step-data">
          <h3>步骤2: 成员ID解析</h3>
          <el-card>
            <p>从群组数据中提取到 <strong>{{ groupData?.FromID?.length || 0 }}</strong> 个成员ID</p>
            <p>这些ID将用于查询具体的用户信息</p>
          </el-card>
        </div>

        <!-- 步骤3: 用户信息获取 -->
        <div v-if="currentStep >= 3" class="step-data">
          <h3>步骤3: 用户信息获取</h3>
          <el-card>
            <p>调用 getImUserList 接口获取所有用户数据</p>
            <p>筛选出ID在 FromID 数组中的用户</p>
            <p>找到 <strong>{{ memberCount }}</strong> 个匹配的群成员</p>
          </el-card>
        </div>

        <!-- 步骤4: 群成员面板 -->
        <div v-if="currentStep >= 4" class="step-data">
          <h3>步骤4: 群成员面板</h3>
          <div class="member-panel-container">
            <GroupUserPanel 
              :group="groupData"
              @member-click="handleMemberClick"
              @start-chat="handleStartChat"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 成员详情弹窗 -->
    <el-dialog v-model="memberDialogVisible" title="成员详情" width="400px">
      <div v-if="selectedMember">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="用户ID">{{ selectedMember.id }}</el-descriptions-item>
          <el-descriptions-item label="昵称">{{ selectedMember.nickname }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ selectedMember.phone }}</el-descriptions-item>
          <el-descriptions-item label="在线状态">
            <el-tag :type="selectedMember.online ? 'success' : 'info'">
              {{ selectedMember.online ? '在线' : '离线' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="角色">
            <el-tag v-if="selectedMember.isOwner" type="danger">群主</el-tag>
            <el-tag v-else-if="selectedMember.isAdmin" type="warning">管理员</el-tag>
            <el-tag v-else type="info">普通成员</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import GroupUserPanel from './components/GroupUserPanel.vue'
import { getGroupList } from '@/api/im/group'

defineOptions({
  name: 'GroupMemberDemo'
})

// 响应式数据
const currentStep = ref(0)
const loading = ref(false)
const groupData = ref(null)
const memberCount = ref(0)
const memberDialogVisible = ref(false)
const selectedMember = ref(null)

// 开始演示
const startDemo = async () => {
  loading.value = true
  currentStep.value = 0

  try {
    // 步骤1: 获取群组数据
    currentStep.value = 1
    await sleep(1000)
    
    const response = await getGroupList({
      page: 1,
      pageSize: 10
    })

    if (response.code === 0 && response.data && response.data.list && response.data.list.length > 0) {
      groupData.value = response.data.list[0]
      
      // 步骤2: 解析成员ID
      currentStep.value = 2
      await sleep(1000)
      
      // 步骤3: 获取用户信息
      currentStep.value = 3
      await sleep(1000)
      
      // 模拟计算成员数量
      memberCount.value = groupData.value.FromID?.length || 0
      
      // 步骤4: 展示成员面板
      currentStep.value = 4
      await sleep(500)
      
      ElMessage.success('演示完成！')
    } else {
      ElMessage.error('没有找到群组数据')
    }
  } catch (error) {
    console.error('演示过程出错:', error)
    ElMessage.error('演示过程出错')
  } finally {
    loading.value = false
  }
}

// 重置演示
const resetDemo = () => {
  currentStep.value = 0
  groupData.value = null
  memberCount.value = 0
  selectedMember.value = null
  memberDialogVisible.value = false
}

// 处理成员点击
const handleMemberClick = (member) => {
  selectedMember.value = member
  memberDialogVisible.value = true
}

// 处理开始私聊
const handleStartChat = (member) => {
  ElMessage.success(`准备与 ${member.nickname} 开始私聊`)
}

// 工具函数：延迟
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms))
</script>

<style lang="scss" scoped>
.group-member-demo {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .demo-header {
    text-align: center;
    margin-bottom: 32px;

    h1 {
      color: #1f2937;
      margin-bottom: 8px;
    }

    p {
      color: #6b7280;
      font-size: 16px;
    }
  }

  .demo-content {
    .steps {
      margin-bottom: 24px;
    }

    .actions {
      text-align: center;
      margin-bottom: 32px;

      .el-button {
        margin: 0 8px;
      }
    }

    .data-display {
      .step-data {
        margin-bottom: 24px;

        h3 {
          color: #374151;
          margin-bottom: 12px;
        }

        .member-ids {
          margin-top: 8px;

          .id-tag {
            margin-right: 8px;
            margin-bottom: 4px;
          }
        }

        .member-panel-container {
          height: 400px;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          overflow: hidden;
        }
      }
    }
  }
}
</style>
